# Environment
ENV=development

# Server Configuration
API_HOST=0.0.0.0
API_PORT=8001

# Security
# Generar una clave segura con: python -m scripts.generate_secret_key
SECRET_KEY=use_a_secure_random_key_at_least_64_chars_long_generated_with_the_script
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
API_KEY_PREFIX=ray_

# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=recommender_db

# Database Roles (required for RLS migrations)
APP_DB_PASSWORD=secure_app_role_password
MAINTENANCE_DB_PASSWORD=secure_maintenance_role_password

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
CACHE_EXPIRE_TIME=30

# API Settings
RATE_LIMIT=100
RATE_LIMIT_PERIOD=60

# Google Cloud (requerido para producción)
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1
GCS_BUCKET_NAME=your-bucket-name
GCS_MODEL_REGISTRY_PATH=models
API_BASE_URL=https://api.example.com  # URL base para callbacks

# CORS
ALLOWED_ORIGINS=["*"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Monitoring
LATENCY_THRESHOLD=1.0
MONITORING_INTERVAL=60

# Configuración de Stripe (legacy)
STRIPE_API_KEY=sk_test_your_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
STRIPE_PRICE_STARTER=price_starter_id
STRIPE_PRICE_PRO=price_pro_id
STRIPE_PRICE_ENTERPRISE=price_enterprise_id

# Configuración de Mercado Pago
MERCADOPAGO_ACCESS_TOKEN=TEST-0000000000000000-000000-00000000000000000000000000000000-000000000
MERCADOPAGO_PUBLIC_KEY=TEST-00000000-0000-0000-0000-000000000000
MERCADOPAGO_WEBHOOK_SECRET=your-mercadopago-webhook-secret
MERCADOPAGO_PRICE_STARTER=starter_price_id
MERCADOPAGO_PRICE_PRO=pro_price_id
MERCADOPAGO_PRICE_ENTERPRISE=enterprise_price_id

# Pasarela de pago a utilizar (stripe o mercadopago)
PAYMENT_GATEWAY=mercadopago

# URL del frontend
FRONTEND_URL=http://localhost:3000