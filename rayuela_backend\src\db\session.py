from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
)
from typing import Optional, AsyncGenerator
from tenacity import retry, stop_after_attempt, wait_exponential
from src.core.config import settings
from src.db.init_models import init_models
from src.utils.base_logger import log_info, log_error
from sqlalchemy import event, text
from sqlalchemy.orm import Session
from contextlib import asynccontextmanager
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id


def set_current_account_id(account_id: Optional[int]):
    """
    Establece el account_id actual en el contexto de la solicitud.
    Thread-safe y async-safe usando contextvars.

    NOTA: Esta función es un wrapper para compatibilidad hacia atrás.
    Usa set_current_tenant_id internamente.
    """
    set_current_tenant_id(account_id)


def get_current_account_id() -> Optional[int]:
    """
    Obtiene el account_id actual del contexto de la solicitud.
    Thread-safe y async-safe usando contextvars.

    NOTA: Esta función es un wrapper para compatibilidad hacia atrás.
    Usa get_current_tenant_id internamente.
    """
    return get_current_tenant_id()


@event.listens_for(Session, "after_begin")
def set_tenant_id(session, transaction, connection):
    """
    Establece el account_id en la sesión de PostgreSQL usando contextvars.
    Esto es seguro para concurrencia ya que cada contexto mantiene su propio valor.
    """
    current_account_id = get_current_tenant_id()
    if current_account_id is not None:
        connection.execute(
            text("SET app.tenant_id = :account_id"), {"account_id": current_account_id}
        )


class DatabaseConnectionManager:
    _instance = None

    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
            await cls._instance.create_engine()
        return cls._instance

    def __init__(self):
        self.engine: Optional[AsyncEngine] = None
        self.session_factory: Optional[async_sessionmaker] = None

    @retry(
        stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def create_engine(self, echo: bool = False):
        try:
            # Configuración de conexión más robusta usando el pool incorporado de SQLAlchemy
            self.engine = create_async_engine(
                url=settings.database_url,
                echo=echo,
                pool_pre_ping=True,
                pool_size=20,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=1800,  # Reciclar conexiones cada 30 minutos
                pool_reset_on_return=True,  # Restablecer la conexión al devolverla al pool
            )

            await self.configure_mappers()

            # Crear session factory con bind asíncrono
            self.session_factory = async_sessionmaker(
                bind=self.engine,
                autoflush=False,
                expire_on_commit=False,
            )

            return self.engine
        except OperationalError as e:
            log_error(f"Error creando el motor de base de datos: {e}")
            raise

    async def configure_mappers(self):
        """Configura los mappers de SQLAlchemy"""
        try:
            # Inicializar todos los modelos
            init_models()

            # Configurar las relaciones
            from sqlalchemy.orm import configure_mappers

            configure_mappers()
            log_info("SQLAlchemy mappers configured successfully")
        except Exception as e:
            log_error(f"Error configuring SQLAlchemy mappers: {e}")
            raise

    @asynccontextmanager
    async def get_session(self):
        """
        DEPRECATED: Use get_db() instead.
        This method is kept for backward compatibility but should not be used in new code.
        Transaction management should be handled explicitly with 'async with db.begin():'
        """
        if not self.session_factory:
            await self.create_engine()

        session = None
        try:
            if not self.session_factory:
                raise ValueError("Session factory not initialized")

            session = self.session_factory()
            yield session
            # No commit/rollback here - transaction management should be explicit
        except Exception as e:
            log_error(f"Database session failed: {e}")
            raise
        finally:
            if session:
                await session.close()

    async def close_engine(self):
        """Cierra el motor de base de datos si está activo"""
        if self.engine:
            await self.engine.dispose()
            log_info("Database engine closed successfully")


async def get_async_session_factory():
    """
    Devuelve el factory de sesiones asíncronas para la base de datos.
    Esta función es útil para los workers de Celery que necesitan crear sesiones
    independientes de la API.

    Returns:
        async_sessionmaker: Factory para crear sesiones asíncronas
    """
    connection_manager = await DatabaseConnectionManager.get_instance()
    if not connection_manager.session_factory:
        log_error("Database session factory not initialized.")
        raise ValueError("Database session factory not initialized.")

    return connection_manager.session_factory


async def get_async_session():
    """
    Crea una nueva sesión asíncrona para la base de datos.
    Esta función es útil para los workers de Celery que necesitan crear sesiones
    independientes de la API.

    Returns:
        AsyncSession: Una nueva sesión asíncrona
    """
    factory = await get_async_session_factory()
    return factory()


@asynccontextmanager
async def get_db() -> AsyncGenerator:
    """
    Proporciona una sesión de base de datos asíncrona.
    Gestiona la conexión y el cierre básico.

    IMPORTANTE: La gestión de transacciones (commit/rollback) debe hacerse explícitamente
    usando 'async with session.begin():' donde se usa la sesión para operaciones de escritura.

    Ejemplo de uso correcto:
    ```python
    @router.post("/resource")
    async def create_resource(resource_data: schemas.ResourceCreate, db: AsyncSession = Depends(get_db)):
        try:
            async with db.begin():  # Inicia una transacción
                # Realizar operaciones de escritura
                repository = ResourceRepository(db)
                resource = await repository.create(resource_data)
                # No es necesario hacer commit explícito
            return resource
        except Exception as e:
            # No es necesario hacer rollback explícito
            log_error(f"Error creating resource: {str(e)}")
            raise
    ```

    Para operaciones de solo lectura, no es necesario usar 'async with db.begin()'.
    """
    connection_manager = await DatabaseConnectionManager.get_instance()
    if not connection_manager.session_factory:
        log_error("Database session factory not initialized.")
        raise HTTPException(status_code=503, detail="Database service unavailable.")

    session: Optional[AsyncSession] = None
    try:
        # Obtener sesión del factory
        session = connection_manager.session_factory()

        # Establecer el account_id en la sesión de PostgreSQL usando contextvars
        current_account_id = get_current_tenant_id()
        if current_account_id is not None:
            await session.execute(
                text("SET app.tenant_id = :account_id"),
                {"account_id": current_account_id},
            )

        # Log para depuración si es necesario
        log_info(f"DB Session {id(session)} acquired.")
        yield session
        # !! NO hay commit/rollback aquí !! La transacción se maneja fuera.
    except SQLAlchemyError as e:
        # Este bloque captura errores durante la *obtención* de la sesión o
        # errores no capturados dentro del 'yield' si no se usa 'session.begin()'.
        log_error(f"Database error in session {id(session)} context: {e}")
        # No podemos hacer rollback si el error ocurrió antes del yield o si ya se hizo.
        # Relanzamos para que el middleware de errores lo capture.
        raise HTTPException(status_code=500, detail="Database operation failed.")
    except Exception as e:
        log_error(f"Unexpected error in get_db for session {id(session)}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error.")
    finally:
        if session:
            await session.close()
            log_info(f"DB Session {id(session)} closed.")
        # else:
        #     log_warning("Attempted to close a session that was not acquired.")


async def init_db():
    """
    Inicializa la base de datos.

    ADVERTENCIA: Esta función está obsoleta y solo se mantiene por compatibilidad.
    En producción, usar SIEMPRE Alembic para migraciones:
        alembic upgrade head

    Para desarrollo local, usar el script init_db.py:
        python -m scripts.init_db
    """
    from src.utils.base_logger import log_warning

    log_warning(
        "La función init_db() está obsoleta. "
        "En producción, usar SIEMPRE 'alembic upgrade head' para migraciones."
    )
    log_warning(
        "Para desarrollo local, usar el script init_db.py: " "python -m scripts.init_db"
    )

    try:
        # Obtener una instancia del gestor de conexiones
        connection_manager = await DatabaseConnectionManager.get_instance()

        # Asegurarse de que el motor está creado
        if not connection_manager.engine:
            await connection_manager.create_engine()

        # Verificar conexión a la base de datos
        from sqlalchemy import text

        async with connection_manager.engine.connect() as conn:
            await conn.execute(text("SELECT 1"))

        log_info("Database connection verified successfully")
        return True
    except Exception as e:
        log_error(f"Error connecting to database: {e}")
        raise
